
import React, { useState, useEffect } from 'react';
import Header from '@/components/Header';
import { Hero } from '@/components/Hero';
import { StatisticsSection } from '@/components/StatisticsSection';
import EducationalSection from '@/components/EducationalSection';
import ModelPerformanceSection from '@/components/ModelPerformanceSection';
import FileUpload from '@/components/FileUpload';
import ImagePreview from '@/components/ImagePreview';
import AnalysisVisualizer from '@/components/AnalysisVisualizer';
import ClinicalIntegration from '@/components/ClinicalIntegration';
import ModelUploader from '@/components/ModelUploader';
import WholeSlideImageUploader from '@/components/WholeSlideImageUploader';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { loadModel } from '@/lib/modelService';

interface AnalysisResult {
  patchId: string;
  stroma: number;
  tumor: number;
  vessel: number;
}

const Index = () => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');

  // Load model on component mount
  useEffect(() => {
    initModel();
  }, []);

  const initModel = async () => {
    try {
      const loaded = await loadModel();
      setModelLoaded(loaded);
    } catch (error) {
      console.error("Error initializing model:", error);
    }
  };

  const handleFilesSelected = (files: File[]) => {
    setUploadedFiles(files);
    setSelectedImageIndex(0);
    setAnalysisResults([]);
  };

  const handleSelectImage = (index: number) => {
    setSelectedImageIndex(index);
  };

  const handleAnalysisComplete = (results: AnalysisResult[]) => {
    setAnalysisResults(results);
  };

  const handleModelUploaded = async () => {
    // Reload the model after a new one is uploaded
    await initModel();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50">
      <Header />

      {/* Hero Section */}
      <Hero />

      {/* Educational Section */}
      <EducationalSection />

      {/* Statistics Section */}
      <StatisticsSection />

      {/* Model Performance Section */}
      <ModelPerformanceSection />

      {/* Analysis Tools Section */}
      <section className="py-16 bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-primary-navy mb-4">
              AI-Powered Analysis Tools
            </h2>
            <p className="text-xl text-medium-gray max-w-3xl mx-auto">
              Upload tissue patches or whole slide images for comprehensive endometrial cancer analysis
              using our advanced TensorFlow.js model.
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            {/* Tabs for Upload, Whole Slide Image, and Model Configuration */}
            <Tabs defaultValue="upload" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full max-w-lg mx-auto grid-cols-3 mb-8 bg-white/90 backdrop-blur-sm shadow-lg">
                <TabsTrigger value="upload" className="text-sm">Upload Patches</TabsTrigger>
                <TabsTrigger value="wsi" className="text-sm">Whole Slide Image</TabsTrigger>
                <TabsTrigger value="model" className="text-sm">Model Config</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="mt-8">
                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8 shadow-lg">
                  <h3 className="text-2xl font-semibold text-primary-navy mb-6">Upload Tissue Patches</h3>
                  <p className="text-medium-gray mb-6">
                    Upload individual tissue patch images for detailed analysis of stroma, tumor, and vessel composition.
                  </p>
                  <FileUpload onFilesSelected={handleFilesSelected} />
                </div>
              </TabsContent>

              <TabsContent value="wsi" className="mt-8">
                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8 shadow-lg">
                  <h3 className="text-2xl font-semibold text-primary-navy mb-6">Whole Slide Image Analysis</h3>
                  <p className="text-medium-gray mb-6">
                    Upload whole slide images in TIF format for automatic patch extraction and comprehensive analysis.
                  </p>
                  <WholeSlideImageUploader onPatchesExtracted={handleFilesSelected} />
                </div>
              </TabsContent>

              <TabsContent value="model" className="mt-8">
                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8 shadow-lg">
                  <h3 className="text-2xl font-semibold text-primary-navy mb-6">Model Configuration</h3>
                  <p className="text-medium-gray mb-6">
                    Configure and upload custom TensorFlow.js models for specialized analysis requirements.
                  </p>
                  <ModelUploader onModelUploaded={handleModelUploaded} />
                </div>
              </TabsContent>
            </Tabs>

            {/* Display & Analysis Section */}
            {uploadedFiles.length > 0 && (
              <div className="mt-16">
                <Separator className="my-8" />

                <div className="grid lg:grid-cols-2 gap-8">
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8 shadow-lg">
                    <h3 className="text-2xl font-semibold text-primary-navy mb-6">Patch Preview</h3>
                    <ImagePreview
                      files={uploadedFiles}
                      selectedIndex={selectedImageIndex}
                      onSelectImage={handleSelectImage}
                    />
                  </div>

                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8 shadow-lg">
                    <h3 className="text-2xl font-semibold text-primary-navy mb-6">AI Analysis Results</h3>
                    <AnalysisVisualizer
                      files={uploadedFiles}
                      selectedIndex={selectedImageIndex}
                      onAnalysisComplete={handleAnalysisComplete}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Clinical Matrix Integration Section */}
            {analysisResults.length > 0 && (
              <div className="mt-16">
                <Separator className="my-8" />

                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8 shadow-lg">
                  <h3 className="text-2xl font-semibold text-primary-navy mb-6">Clinical Integration</h3>
                  <p className="text-medium-gray mb-6">
                    Comprehensive analysis results formatted for clinical decision-making and reporting.
                  </p>
                  <ClinicalIntegration analysisResults={analysisResults} />
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
